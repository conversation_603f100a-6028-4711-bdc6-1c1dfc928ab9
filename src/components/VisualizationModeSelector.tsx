'use client';

import React from 'react';
import { Bar<PERSON><PERSON>3, Grid3X3, <PERSON><PERSON>er3<PERSON>, <PERSON> } from 'lucide-react';
import { VisualizationMode } from '@/types/crypto';
import { VISUALIZATION_MODES } from '@/constants';

interface VisualizationModeSelectorProps {
  currentMode: VisualizationMode['id'];
  onModeChange: (mode: VisualizationMode['id']) => void;
  className?: string;
}

const modeIcons = {
  bubble: BarChart3,
  treemap: Grid3X3,
  scatter: Scatter3D,
  heatmap: Flame,
};

export default function VisualizationModeSelector({
  currentMode,
  onModeChange,
  className = '',
}: VisualizationModeSelectorProps) {
  return (
    <div className={`bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 ${className}`}>
      <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
        Visualization Mode
      </h3>
      
      <div className="grid grid-cols-2 gap-2">
        {VISUALIZATION_MODES.map((mode) => {
          const Icon = modeIcons[mode.id];
          const isActive = currentMode === mode.id;
          const isDisabled = mode.id !== 'bubble'; // Only bubble chart is implemented for now
          
          return (
            <button
              key={mode.id}
              onClick={() => !isDisabled && onModeChange(mode.id)}
              disabled={isDisabled}
              className={`
                relative p-3 rounded-lg border-2 transition-all duration-200 text-left
                ${isActive 
                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' 
                  : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
                }
                ${isDisabled 
                  ? 'opacity-50 cursor-not-allowed' 
                  : 'cursor-pointer hover:shadow-sm'
                }
              `}
              title={isDisabled ? 'Coming soon' : mode.description}
            >
              <div className="flex items-center space-x-2 mb-1">
                <Icon className={`w-4 h-4 ${
                  isActive 
                    ? 'text-blue-600 dark:text-blue-400' 
                    : 'text-gray-500 dark:text-gray-400'
                }`} />
                <span className={`text-sm font-medium ${
                  isActive 
                    ? 'text-blue-900 dark:text-blue-100' 
                    : 'text-gray-900 dark:text-white'
                }`}>
                  {mode.name}
                </span>
              </div>
              
              <p className={`text-xs ${
                isActive 
                  ? 'text-blue-700 dark:text-blue-300' 
                  : 'text-gray-600 dark:text-gray-400'
              }`}>
                {mode.description}
              </p>
              
              {isDisabled && (
                <div className="absolute top-1 right-1">
                  <span className="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                    Soon
                  </span>
                </div>
              )}
              
              {isActive && (
                <div className="absolute top-1 right-1">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                </div>
              )}
            </button>
          );
        })}
      </div>
      
      <div className="mt-3 text-xs text-gray-500 dark:text-gray-400">
        More visualization modes coming soon!
      </div>
    </div>
  );
}
